{"registers": [{"var_name": "UID0", "alias": "设备唯一标识符", "permission": "r", "address": 0, "data_type": "uint32_t", "range": [0, 4294967295], "unit": "", "confirm_dialog": false, "step_size": 1}, {"var_name": "SELF_IP", "alias": "本机IP地址", "permission": "rw", "address": 1, "data_type": "ipv4", "range": [0, 4294967295], "unit": "", "confirm_dialog": true, "step_size": 1}, {"var_name": "GATEWAY_IP", "alias": "网关IP地址", "permission": "rw", "address": 2, "data_type": "ipv4", "range": [0, 4294967295], "unit": "", "confirm_dialog": true, "step_size": 1}, {"var_name": "NETMASK", "alias": "子网掩码", "permission": "rw", "address": 3, "data_type": "ipv4", "range": [0, 4294967295], "unit": "", "confirm_dialog": true, "step_size": 1}, {"var_name": "MAC_ADDR_LOW", "alias": "MAC地址低4字节", "permission": "rw", "address": 4, "data_type": "uint32_t", "range": [-2147483648, 2147483647], "unit": "", "confirm_dialog": true, "step_size": 1}, {"var_name": "DEST_IP", "alias": "目标IP地址", "permission": "rw", "address": 5, "data_type": "ipv4", "range": [0, 4294967295], "unit": "", "confirm_dialog": true, "step_size": 1}, {"var_name": "PORT_CONFIG", "alias": "端口配置", "permission": "rw", "address": 6, "data_type": "dual_uint16", "range": [0, 4294967295], "unit": "", "confirm_dialog": true, "step_size": 1}, {"var_name": "RESERVED_7", "alias": "保留寄存器7", "permission": "rw", "address": 7, "data_type": "uint32_t", "range": [0, 65535], "unit": "", "confirm_dialog": false, "step_size": 1}, {"var_name": "EPWM_PERIOD_Base", "alias": "EPWM基础周期", "permission": "rw", "address": 8, "data_type": "uint32_t", "range": [1, 65535], "unit": "计数", "confirm_dialog": true, "step_size": 1}, {"var_name": "EPWM_DB", "alias": "EPWM死区时间", "permission": "rw", "address": 9, "data_type": "uint32_t", "range": [0, 1023], "unit": "计数", "confirm_dialog": true, "step_size": 1}, {"var_name": "TIMER0_PRD", "alias": "定时器0周期", "permission": "rw", "address": 10, "data_type": "uint32_t", "range": [1, 65535], "unit": "计数", "confirm_dialog": true, "step_size": 1}, {"var_name": "TIMER1_PRD", "alias": "定时器1周期", "permission": "rw", "address": 11, "data_type": "uint32_t", "range": [1, 65535], "unit": "计数", "confirm_dialog": true, "step_size": 1}, {"var_name": "DutyMAX", "alias": "最大占空比", "permission": "rw", "address": 12, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 1.0], "unit": "", "confirm_dialog": true, "step_size": 0.01}, {"var_name": "DutyMIN", "alias": "最小占空比", "permission": "rw", "address": 13, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 1.0], "unit": "", "confirm_dialog": true, "step_size": 0.01}, {"var_name": "Rottx_Zero_Current", "alias": "旋变零点电流", "permission": "rw", "address": 14, "data_type": "fixed_point", "scale_factor": 100000, "range": [-1000.0, 1000.0], "unit": "A", "confirm_dialog": true, "step_size": 0.1}, {"var_name": "PID_Speed_Kp", "alias": "速度环Kp", "permission": "rw", "address": 15, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 1000.0], "unit": "", "confirm_dialog": true, "step_size": 0.001}, {"var_name": "PID_Speed_Ki", "alias": "速度环Ki", "permission": "rw", "address": 16, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 1000.0], "unit": "", "confirm_dialog": true, "step_size": 0.001}, {"var_name": "PID_Speed_Kd", "alias": "速度环Kd", "permission": "rw", "address": 17, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 1000.0], "unit": "", "confirm_dialog": true, "step_size": 0.001}, {"var_name": "PID_Speed_Kd_Filter", "alias": "速度环Kd滤波", "permission": "rw", "address": 18, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 1000.0], "unit": "", "confirm_dialog": true, "step_size": 0.001}, {"var_name": "PID_Id_Kp", "alias": "Id环Kp", "permission": "rw", "address": 19, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 1000.0], "unit": "", "confirm_dialog": true, "step_size": 0.001}, {"var_name": "PID_Id_Ki", "alias": "Id环Ki", "permission": "rw", "address": 20, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 1000.0], "unit": "", "confirm_dialog": true, "step_size": 0.001}, {"var_name": "PID_Id_Kd", "alias": "Id环Kd", "permission": "rw", "address": 21, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 1000.0], "unit": "", "confirm_dialog": true, "step_size": 0.001}, {"var_name": "PID_Id_Kd_Filter", "alias": "Id环Kd滤波", "permission": "rw", "address": 22, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 1000.0], "unit": "", "confirm_dialog": true, "step_size": 0.001}, {"var_name": "PID_Iq_Kp", "alias": "Iq环Kp", "permission": "rw", "address": 23, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 1000.0], "unit": "", "confirm_dialog": true, "step_size": 0.001}, {"var_name": "PID_Iq_Ki", "alias": "Iq环Ki", "permission": "rw", "address": 24, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 1000.0], "unit": "", "confirm_dialog": true, "step_size": 0.001}, {"var_name": "PID_Iq_Kd", "alias": "Iq环Kd", "permission": "rw", "address": 25, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 1000.0], "unit": "", "confirm_dialog": true, "step_size": 0.001}, {"var_name": "PID_Iq_Kd_Filter", "alias": "Iq环Kd滤波", "permission": "rw", "address": 26, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 1000.0], "unit": "", "confirm_dialog": true, "step_size": 0.001}, {"var_name": "Motor_Id_Max", "alias": "电机Id最大值", "permission": "rw", "address": 27, "data_type": "fixed_point", "scale_factor": 100000, "range": [-1000.0, 1000.0], "unit": "A", "confirm_dialog": true, "step_size": 0.1}, {"var_name": "Motor_Id_Min", "alias": "电机Id最小值", "permission": "rw", "address": 28, "data_type": "fixed_point", "scale_factor": 100000, "range": [-1000.0, 1000.0], "unit": "A", "confirm_dialog": true, "step_size": 0.1}, {"var_name": "<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>", "alias": "电机Id最小值(重复)", "permission": "rw", "address": 29, "data_type": "fixed_point", "scale_factor": 100000, "range": [-1000.0, 1000.0], "unit": "A", "confirm_dialog": true, "step_size": 0.1}, {"var_name": "Motor_Id_Max_Lowspeed", "alias": "低速Id最大值", "permission": "rw", "address": 30, "data_type": "fixed_point", "scale_factor": 100000, "range": [-1000.0, 1000.0], "unit": "A", "confirm_dialog": true, "step_size": 0.1}, {"var_name": "Motor_Id_Min_Lowspeed", "alias": "低速Id最小值", "permission": "rw", "address": 31, "data_type": "fixed_point", "scale_factor": 100000, "range": [-1000.0, 1000.0], "unit": "A", "confirm_dialog": true, "step_size": 0.1}, {"var_name": "Motor_Iq_Max", "alias": "电机Iq最大值", "permission": "rw", "address": 32, "data_type": "fixed_point", "scale_factor": 100000, "range": [-1000.0, 1000.0], "unit": "A", "confirm_dialog": true, "step_size": 0.1}, {"var_name": "Motor_Iq_Min", "alias": "电机Iq最小值", "permission": "rw", "address": 33, "data_type": "fixed_point", "scale_factor": 100000, "range": [-1000.0, 1000.0], "unit": "A", "confirm_dialog": true, "step_size": 0.1}, {"var_name": "Motor_Iq_Max_Lowspeed", "alias": "低速Iq最大值", "permission": "rw", "address": 34, "data_type": "fixed_point", "scale_factor": 100000, "range": [-1000.0, 1000.0], "unit": "A", "confirm_dialog": true, "step_size": 0.1}, {"var_name": "Motor_Iq_Min_Lowspeed", "alias": "低速Iq最小值", "permission": "rw", "address": 35, "data_type": "fixed_point", "scale_factor": 100000, "range": [-1000.0, 1000.0], "unit": "A", "confirm_dialog": true, "step_size": 0.1}, {"var_name": "Motor_Speed_Max", "alias": "电机最大转速", "permission": "rw", "address": 36, "data_type": "fixed_point", "scale_factor": 100000, "range": [-10000.0, 10000.0], "unit": "rpm", "confirm_dialog": true, "step_size": 1.0}, {"var_name": "Motor_Speed_Min", "alias": "电机最小转速", "permission": "rw", "address": 37, "data_type": "fixed_point", "scale_factor": 100000, "range": [-10000.0, 10000.0], "unit": "rpm", "confirm_dialog": true, "step_size": 1.0}, {"var_name": "Reserved1", "alias": "保留参数1", "permission": "rw", "address": 38, "data_type": "fixed_point", "scale_factor": 100000, "range": [-1000.0, 1000.0], "unit": "", "confirm_dialog": false, "step_size": 0.1}, {"var_name": "Reserved2", "alias": "保留参数2", "permission": "rw", "address": 39, "data_type": "fixed_point", "scale_factor": 100000, "range": [-1000.0, 1000.0], "unit": "", "confirm_dialog": false, "step_size": 0.1}, {"var_name": "Motor_Ld", "alias": "电机d轴电感", "permission": "rw", "address": 40, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 1.0], "unit": "H", "confirm_dialog": true, "step_size": 1e-05}, {"var_name": "Motor_Ld_Inv", "alias": "电机d轴电感倒数", "permission": "rw", "address": 41, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 10000.0], "unit": "1/H", "confirm_dialog": true, "step_size": 0.1}, {"var_name": "Motor_Lq", "alias": "电机q轴电感", "permission": "rw", "address": 42, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 1.0], "unit": "H", "confirm_dialog": true, "step_size": 1e-05}, {"var_name": "Motor_Lq_Inv", "alias": "电机q轴电感倒数", "permission": "rw", "address": 43, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 10000.0], "unit": "1/H", "confirm_dialog": true, "step_size": 0.1}, {"var_name": "Motor_Flux", "alias": "电机磁链", "permission": "rw", "address": 44, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 10.0], "unit": "Wb", "confirm_dialog": true, "step_size": 0.001}, {"var_name": "Motor_Rs", "alias": "电机定子电阻", "permission": "rw", "address": 45, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.0, 100.0], "unit": "Ω", "confirm_dialog": true, "step_size": 0.001}, {"var_name": "Motor_Pn", "alias": "电机极对数", "permission": "rw", "address": 46, "data_type": "fixed_point", "scale_factor": 100000, "range": [1.0, 50.0], "unit": "", "confirm_dialog": true, "step_size": 1.0}, {"var_name": "Motor_Pn_Inv", "alias": "电机极对数倒数", "permission": "rw", "address": 47, "data_type": "fixed_point", "scale_factor": 100000, "range": [0.02, 1.0], "unit": "", "confirm_dialog": true, "step_size": 0.01}, {"var_name": "Motor_Resolver_Zero", "alias": "旋变零点", "permission": "rw", "address": 48, "data_type": "fixed_point", "scale_factor": 100000, "range": [-3.14159, 3.14159], "unit": "rad", "confirm_dialog": true, "step_size": 0.01}, {"var_name": "Set_Speed_Ref", "alias": "设定转速参考", "permission": "rw", "address": 49, "data_type": "fixed_point", "scale_factor": 100000, "range": [-3000.0, 3000.0], "unit": "rpm", "confirm_dialog": false, "step_size": 0.1}, {"var_name": "mEncrypt", "alias": "加密标志", "permission": "rw", "address": 50, "data_type": "uint32_t", "range": [0, 1000], "unit": "", "confirm_dialog": true, "step_size": 1}], "status_registers": [{"var_name": "MotorPosition_ResovlerFault", "alias": "电机位置/旋变故障", "permission": "r", "address": 55, "data_type": "dual_uint16", "unit": ""}, {"var_name": "mAngle_Reserved", "alias": "角度/保留", "permission": "r", "address": 56, "data_type": "dual_uint16", "unit": ""}, {"var_name": "mEtheta", "alias": "电角度", "permission": "r", "address": 57, "data_type": "fixed_point", "scale_factor": 100000, "unit": "rad"}, {"var_name": "mEtheta1", "alias": "电角度1", "permission": "r", "address": 58, "data_type": "fixed_point", "scale_factor": 100000, "unit": "rad"}, {"var_name": "mEthetaAVG", "alias": "平均电角度", "permission": "r", "address": 59, "data_type": "fixed_point", "scale_factor": 100000, "unit": "rad"}, {"var_name": "mEthetaRad", "alias": "电角度弧度", "permission": "r", "address": 60, "data_type": "fixed_point", "scale_factor": 100000, "unit": "rad"}, {"var_name": "mEthetaZero", "alias": "电角度零点", "permission": "r", "address": 61, "data_type": "fixed_point", "scale_factor": 100000, "unit": "rad"}, {"var_name": "Vdd_P_3V3", "alias": "3.3V电源电压", "permission": "r", "address": 62, "data_type": "fixed_point", "scale_factor": 100000, "unit": "V"}, {"var_name": "mPT1", "alias": "压力传感器1", "permission": "r", "address": 63, "data_type": "fixed_point", "scale_factor": 100000, "unit": "Pa"}, {"var_name": "mPT2", "alias": "压力传感器2", "permission": "r", "address": 64, "data_type": "fixed_point", "scale_factor": 100000, "unit": "Pa"}, {"var_name": "mPT3", "alias": "压力传感器3", "permission": "r", "address": 65, "data_type": "fixed_point", "scale_factor": 100000, "unit": "Pa"}, {"var_name": "mPT4", "alias": "压力传感器4", "permission": "r", "address": 66, "data_type": "fixed_point", "scale_factor": 100000, "unit": "Pa"}, {"var_name": "mPT5", "alias": "压力传感器5", "permission": "r", "address": 67, "data_type": "fixed_point", "scale_factor": 100000, "unit": "Pa"}, {"var_name": "Vdd_P_5V", "alias": "5V电源电压", "permission": "r", "address": 68, "data_type": "fixed_point", "scale_factor": 100000, "unit": "V"}, {"var_name": "mNTC1", "alias": "NTC温度传感器1", "permission": "r", "address": 69, "data_type": "fixed_point", "scale_factor": 100000, "unit": "°C"}, {"var_name": "mNTC2", "alias": "NTC温度传感器2", "permission": "r", "address": 70, "data_type": "fixed_point", "scale_factor": 100000, "unit": "°C"}, {"var_name": "mTempNTC1", "alias": "NTC1温度值", "permission": "r", "address": 71, "data_type": "fixed_point", "scale_factor": 100000, "unit": "°C"}, {"var_name": "mTempNTC2", "alias": "NTC2温度值", "permission": "r", "address": 72, "data_type": "fixed_point", "scale_factor": 100000, "unit": "°C"}, {"var_name": "Ia", "alias": "A相电流", "permission": "r", "address": 73, "data_type": "fixed_point", "scale_factor": 100000, "unit": "A"}, {"var_name": "Ib", "alias": "B相电流", "permission": "r", "address": 74, "data_type": "fixed_point", "scale_factor": 100000, "unit": "A"}, {"var_name": "Ic", "alias": "C相电流", "permission": "r", "address": 75, "data_type": "fixed_point", "scale_factor": 100000, "unit": "A"}, {"var_name": "Vbus", "alias": "母线电压", "permission": "r", "address": 76, "data_type": "fixed_point", "scale_factor": 100000, "unit": "V"}, {"var_name": "Vbus_protect", "alias": "母线保护电压", "permission": "r", "address": 77, "data_type": "fixed_point", "scale_factor": 100000, "unit": "V"}, {"var_name": "Vbus_filter", "alias": "母线滤波电压", "permission": "r", "address": 78, "data_type": "fixed_point", "scale_factor": 100000, "unit": "V"}, {"var_name": "Ibus", "alias": "母线电流", "permission": "r", "address": 79, "data_type": "fixed_point", "scale_factor": 100000, "unit": "A"}, {"var_name": "Motor_RpstoRpm_COEF", "alias": "转速转换系数", "permission": "r", "address": 80, "data_type": "fixed_point", "scale_factor": 100000, "unit": ""}, {"var_name": "Last_Set_Speed_Ref", "alias": "上次设定转速", "permission": "r", "address": 81, "data_type": "fixed_point", "scale_factor": 100000, "unit": "rpm"}, {"var_name": "RampSet_Speed_Ref", "alias": "斜坡设定转速", "permission": "r", "address": 82, "data_type": "fixed_point", "scale_factor": 100000, "unit": "rpm"}, {"var_name": "mSpeed", "alias": "实际转速", "permission": "r", "address": 83, "data_type": "fixed_point", "scale_factor": 100000, "unit": "rpm"}, {"var_name": "m<PERSON>uty", "alias": "实际占空比", "permission": "r", "address": 84, "data_type": "fixed_point", "scale_factor": 100000, "unit": ""}, {"var_name": "we", "alias": "电角速度", "permission": "r", "address": 85, "data_type": "fixed_point", "scale_factor": 100000, "unit": "rad/s"}, {"var_name": "mTe_ref", "alias": "参考转矩", "permission": "r", "address": 86, "data_type": "fixed_point", "scale_factor": 100000, "unit": "Nm"}, {"var_name": "Va", "alias": "A相电压", "permission": "r", "address": 87, "data_type": "fixed_point", "scale_factor": 100000, "unit": "V"}, {"var_name": "Vb", "alias": "B相电压", "permission": "r", "address": 88, "data_type": "fixed_point", "scale_factor": 100000, "unit": "V"}, {"var_name": "Vc", "alias": "C相电压", "permission": "r", "address": 89, "data_type": "fixed_point", "scale_factor": 100000, "unit": "V"}], "commands": [{"var_name": "START_CMD", "alias": "启动命令", "permission": "w", "address": 105, "data_type": "uint32_t", "command_value": 193, "unit": "", "confirm_dialog": true, "hotkey": "F1"}, {"var_name": "STOP_CMD", "alias": "停止命令", "permission": "w", "address": 105, "data_type": "uint32_t", "command_value": 241, "unit": "", "confirm_dialog": true, "hotkey": "F2"}]}