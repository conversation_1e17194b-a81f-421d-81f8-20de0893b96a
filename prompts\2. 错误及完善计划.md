使用中文和我交流。

# 前述任务评价

1. 前面的任务完成的的总体上还可以，存在几处错误我帮你修正了：
1.1 UDP通信包头解析你搞错了是小端序，你弄反了，我不能修正了。
1.2 UDP的asyncio没有正确进行调度，我在main.py里面帮你修正了。
1.3 示波器的CursorControlWidget的添加和显示没有正确隐藏我在main.py里面帮你修正了。

**任务1**：请先将前述任务进行本地git提交。

# 核心改进任务：示波器缩放偏移功能完善

当前示波器的缩放和偏移功能不符合专业示波器的操作习惯，需要按照以下详细规范进行重构：

2. 现在示波器的使用上面还在存在的重大问题：
2.1 缩放和偏移功能要和正常示波器一模一样，分为两类，垂直挡位和水平挡位缩放/偏移。

## 采样频率问题

然后是水平挡位，每个类型0xA1、0xA2的电机采样数据就是一个采样点，但是现在没有配置采样频率的功能，需要增加采样频率配置的功能，通过采样频率计算出正确的时基挡位值、X轴的值、光标的计算值等。

## 水平缩放X

- 水平挡位缩放是全局所有通道的缩放，功能我希望鼠标滚轮的默认缩放功能是水平挡位缩放，垂直不缩放这个时候。
- 当水平挡位缩放的时候右侧的水平挡位设置的spinbox的值也要相应的改变，并且保存到配置文件中，注意不要产生循环调用。
- 当右侧的水平挡位设置的spinbox的值改变的时候，水平缩放也要相应的变化。
- X轴显示的每格的值也要相应的变化，光标的计算结果要正确。
- X轴的值最右侧新出现的点就是X轴的原点，X坐标其实就是都是负数了。

## 水平偏移X

- 水平偏移是全局所有通道的偏移，目前鼠标左键已经实现了垂直和水平偏移的功能，并且也是整个坐标的偏移，符合我的要求。但是我希望禁止垂直方向的坐标偏移，因为垂直方向实际上是偏移个通道曲线的显示位置，不算整个坐标的偏移。
- 当水平偏移的时候右侧的水平偏移设置的spinbox的值也要相应的改变，并且保存到配置文件中，注意不要产生循环调用。
- 当右侧的水平偏移设置的spinbox的值改变的时候，水平偏移也要相应的变化。

## 垂直缩放Y

- 垂直挡位缩放是每个通道的缩放，每个通道的缩放是独立的，并且是作用于曲线的显示，而不是作用于整个坐标的缩放，且只是显示渲染，数据缓存当然是缓存原始数据，相互是解耦的，显示是显示存储是存储。
- 我希望当按住键盘的CTRL键的时候，鼠标滚轮缩放当前选中的通道的垂直挡位，垂直挡位spinbox的值也要相应的改变，并且保存到配置文件中，注意不要产生循环调用。
- 当右侧的垂直挡位设置的spinbox的值改变的时候，垂直缩放也要相应的变化。
- Y轴显示的每格的值不需要相应的变化因为每个通道不一样，只要光标计算的结果正确即可。

## 垂直偏移Y

- 垂直偏移是每个通道的偏移，每个通道的偏移是独立的，并且是作用于曲线的显示，而不是作用于整个坐标的偏移，且只是显示渲染，数据缓存当然是缓存原始数据，相互是解耦的，显示是显示存储是存储。
- 我希望当按住键盘的CTRL键的时候，按住鼠标左键上下来改变当前选中的通道的垂直偏移，这样可以减少误操作，相应通道的垂直偏移spinbox的值也要相应的改变，并且保存到配置文件中，注意不要产生循环调用。
- 当右侧的相应通道的垂直偏移设置的spinbox的值改变的时候，垂直偏移也要相应的变化。

## 滚动显示的问题

现在滚动显示不要正常，前面说了新出现的点就是X轴的原点，开启滚动模式的时候实际上就是把X轴的原点固定在最右侧，新的点从右侧出现，旧的点从左侧消失。
关闭滚动模式的时候其实就是前面可以自由调节水平挡位偏移的情况，现在的代码就是这种，鼠标左键按下可以自由调节坐标的位置。

**当前问题**：通道显示开关操作繁琐，需要切换tab页

**改进方案**：
- 在主界面添加通道显示开关按钮组
- 每个通道一个切换按钮，显示通道名称和颜色
- 点击即可快速开启/关闭对应通道显示
- 按钮状态与通道配置同步

## 技术实现要求

1. **性能优化**：
   - 确保OpenGL加速正常工作
   - 正确使用pyqtgraph的PlotWidget和PlotItem
   - 避免不必要的重绘和数据拷贝

2. **代码质量**：
   - 避免UI控件与数据处理的循环调用
   - 保持显示渲染与数据存储的解耦
   - 确保配置变化的正确持久化

3. **用户体验**：
   - 鼠标和键盘交互符合专业示波器习惯
   - 所有操作提供即时视觉反馈
   - 配置变化自动保存，无需手动操作

请按照上述详细规范修改示波器UI代码，重点关注缩放偏移功能的正确实现和用户交互体验的优化。