# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'src\ui\device_setting.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(1130, 667)
        self.gridLayout_3 = QtWidgets.QGridLayout(Form)
        self.gridLayout_3.setObjectName("gridLayout_3")
        self.CardWidget = CardWidget(Form)
        self.CardWidget.setObjectName("CardWidget")
        self.gridLayout = QtWidgets.QGridLayout(self.CardWidget)
        self.gridLayout.setObjectName("gridLayout")
        self.table_pid_param = TableWidget(self.CardWidget)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.table_pid_param.setFont(font)
        self.table_pid_param.setObjectName("table_pid_param")
        self.table_pid_param.setColumnCount(0)
        self.table_pid_param.setRowCount(0)
        self.gridLayout.addWidget(self.table_pid_param, 1, 1, 1, 1)
        self.gridLayout_3.addWidget(self.CardWidget, 2, 0, 1, 2)
        self.LargeTitleLabel = LargeTitleLabel(Form)
        font = QtGui.QFont()
        font.setFamily("华文行楷")
        font.setPointSize(44)
        font.setBold(False)
        font.setWeight(50)
        self.LargeTitleLabel.setFont(font)
        self.LargeTitleLabel.setObjectName("LargeTitleLabel")
        self.gridLayout_3.addWidget(self.LargeTitleLabel, 0, 1, 1, 2)
        spacerItem = QtWidgets.QSpacerItem(370, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.gridLayout_3.addItem(spacerItem, 0, 3, 1, 1)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.gridLayout_3.addItem(spacerItem1, 0, 0, 1, 1)
        self.CardWidget_2 = CardWidget(Form)
        self.CardWidget_2.setObjectName("CardWidget_2")
        self.gridLayout_2 = QtWidgets.QGridLayout(self.CardWidget_2)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.label_speed_2 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_2.sizePolicy().hasHeightForWidth())
        self.label_speed_2.setSizePolicy(sizePolicy)
        self.label_speed_2.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_2.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_2.setAlignment(QtCore.Qt.AlignCenter)
        self.label_speed_2.setObjectName("label_speed_2")
        self.gridLayout_2.addWidget(self.label_speed_2, 0, 1, 1, 1)
        self.label_speed_3 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_3.sizePolicy().hasHeightForWidth())
        self.label_speed_3.setSizePolicy(sizePolicy)
        self.label_speed_3.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_3.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_3.setAlignment(QtCore.Qt.AlignCenter)
        self.label_speed_3.setObjectName("label_speed_3")
        self.gridLayout_2.addWidget(self.label_speed_3, 0, 3, 1, 1)
        self.label_speed = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed.sizePolicy().hasHeightForWidth())
        self.label_speed.setSizePolicy(sizePolicy)
        self.label_speed.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed.setObjectName("label_speed")
        self.gridLayout_2.addWidget(self.label_speed, 1, 0, 1, 1)
        self.spinbox_speed_pid_p = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_speed_pid_p.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_speed_pid_p.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_speed_pid_p.setDecimals(6)
        self.spinbox_speed_pid_p.setMaximum(50.0)
        self.spinbox_speed_pid_p.setSingleStep(0.0001)
        self.spinbox_speed_pid_p.setObjectName("spinbox_speed_pid_p")
        self.gridLayout_2.addWidget(self.spinbox_speed_pid_p, 1, 1, 1, 1)
        self.btn_speed_pid_p = ToolButton(self.CardWidget_2)
        self.btn_speed_pid_p.setMinimumSize(QtCore.QSize(44, 44))
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap("src\\ui\\../../img/save2.svg"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        self.btn_speed_pid_p.setIcon(icon)
        self.btn_speed_pid_p.setIconSize(QtCore.QSize(22, 22))
        self.btn_speed_pid_p.setObjectName("btn_speed_pid_p")
        self.gridLayout_2.addWidget(self.btn_speed_pid_p, 1, 2, 1, 1)
        self.spinbox_speed_pid_i = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_speed_pid_i.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_speed_pid_i.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_speed_pid_i.setDecimals(6)
        self.spinbox_speed_pid_i.setMaximum(50.0)
        self.spinbox_speed_pid_i.setSingleStep(0.0001)
        self.spinbox_speed_pid_i.setObjectName("spinbox_speed_pid_i")
        self.gridLayout_2.addWidget(self.spinbox_speed_pid_i, 1, 3, 1, 1)
        self.btn_speed_pid_i = ToolButton(self.CardWidget_2)
        self.btn_speed_pid_i.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_speed_pid_i.setIcon(icon)
        self.btn_speed_pid_i.setIconSize(QtCore.QSize(22, 22))
        self.btn_speed_pid_i.setObjectName("btn_speed_pid_i")
        self.gridLayout_2.addWidget(self.btn_speed_pid_i, 1, 4, 1, 1)
        self.label_speed_5 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_5.sizePolicy().hasHeightForWidth())
        self.label_speed_5.setSizePolicy(sizePolicy)
        self.label_speed_5.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_5.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_5.setObjectName("label_speed_5")
        self.gridLayout_2.addWidget(self.label_speed_5, 2, 0, 1, 1)
        self.spinbox_id_pid_p = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_id_pid_p.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_id_pid_p.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_id_pid_p.setDecimals(6)
        self.spinbox_id_pid_p.setMaximum(50.0)
        self.spinbox_id_pid_p.setSingleStep(0.0001)
        self.spinbox_id_pid_p.setObjectName("spinbox_id_pid_p")
        self.gridLayout_2.addWidget(self.spinbox_id_pid_p, 2, 1, 1, 1)
        self.btn_id_pid_p = ToolButton(self.CardWidget_2)
        self.btn_id_pid_p.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_id_pid_p.setIcon(icon)
        self.btn_id_pid_p.setIconSize(QtCore.QSize(22, 22))
        self.btn_id_pid_p.setObjectName("btn_id_pid_p")
        self.gridLayout_2.addWidget(self.btn_id_pid_p, 2, 2, 1, 1)
        self.spinbox_id_pid_i = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_id_pid_i.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_id_pid_i.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_id_pid_i.setDecimals(6)
        self.spinbox_id_pid_i.setMaximum(50.0)
        self.spinbox_id_pid_i.setSingleStep(0.0001)
        self.spinbox_id_pid_i.setObjectName("spinbox_id_pid_i")
        self.gridLayout_2.addWidget(self.spinbox_id_pid_i, 2, 3, 1, 1)
        self.btn_id_pid_i = ToolButton(self.CardWidget_2)
        self.btn_id_pid_i.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_id_pid_i.setIcon(icon)
        self.btn_id_pid_i.setIconSize(QtCore.QSize(22, 22))
        self.btn_id_pid_i.setObjectName("btn_id_pid_i")
        self.gridLayout_2.addWidget(self.btn_id_pid_i, 2, 4, 1, 1)
        self.label_speed_6 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_6.sizePolicy().hasHeightForWidth())
        self.label_speed_6.setSizePolicy(sizePolicy)
        self.label_speed_6.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_6.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_6.setObjectName("label_speed_6")
        self.gridLayout_2.addWidget(self.label_speed_6, 3, 0, 1, 1)
        self.spinbox_iq_pid_p = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_iq_pid_p.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_iq_pid_p.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_iq_pid_p.setDecimals(6)
        self.spinbox_iq_pid_p.setMaximum(50.0)
        self.spinbox_iq_pid_p.setSingleStep(0.0001)
        self.spinbox_iq_pid_p.setObjectName("spinbox_iq_pid_p")
        self.gridLayout_2.addWidget(self.spinbox_iq_pid_p, 3, 1, 1, 1)
        self.btn_iq_pid_p = ToolButton(self.CardWidget_2)
        self.btn_iq_pid_p.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_iq_pid_p.setIcon(icon)
        self.btn_iq_pid_p.setIconSize(QtCore.QSize(22, 22))
        self.btn_iq_pid_p.setObjectName("btn_iq_pid_p")
        self.gridLayout_2.addWidget(self.btn_iq_pid_p, 3, 2, 1, 1)
        self.spinbox_iq_pid_i = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_iq_pid_i.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_iq_pid_i.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_iq_pid_i.setDecimals(6)
        self.spinbox_iq_pid_i.setMaximum(50.0)
        self.spinbox_iq_pid_i.setSingleStep(0.0001)
        self.spinbox_iq_pid_i.setObjectName("spinbox_iq_pid_i")
        self.gridLayout_2.addWidget(self.spinbox_iq_pid_i, 3, 3, 1, 1)
        self.btn_iq_pid_i = ToolButton(self.CardWidget_2)
        self.btn_iq_pid_i.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_iq_pid_i.setIcon(icon)
        self.btn_iq_pid_i.setIconSize(QtCore.QSize(22, 22))
        self.btn_iq_pid_i.setObjectName("btn_iq_pid_i")
        self.gridLayout_2.addWidget(self.btn_iq_pid_i, 3, 4, 1, 1)
        self.label_speed_7 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_7.sizePolicy().hasHeightForWidth())
        self.label_speed_7.setSizePolicy(sizePolicy)
        self.label_speed_7.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_7.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_7.setObjectName("label_speed_7")
        self.gridLayout_2.addWidget(self.label_speed_7, 4, 0, 1, 1)
        self.spinbox_vbus_ref = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_vbus_ref.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_vbus_ref.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_vbus_ref.setDecimals(6)
        self.spinbox_vbus_ref.setMaximum(50.0)
        self.spinbox_vbus_ref.setSingleStep(0.0001)
        self.spinbox_vbus_ref.setObjectName("spinbox_vbus_ref")
        self.gridLayout_2.addWidget(self.spinbox_vbus_ref, 4, 1, 1, 1)
        self.btn_vbus_ref = ToolButton(self.CardWidget_2)
        self.btn_vbus_ref.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_vbus_ref.setIcon(icon)
        self.btn_vbus_ref.setIconSize(QtCore.QSize(22, 22))
        self.btn_vbus_ref.setObjectName("btn_vbus_ref")
        self.gridLayout_2.addWidget(self.btn_vbus_ref, 4, 2, 1, 1)
        self.spinbox_vbus_ref1 = DoubleSpinBox(self.CardWidget_2)
        self.spinbox_vbus_ref1.setMinimumSize(QtCore.QSize(0, 44))
        self.spinbox_vbus_ref1.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinbox_vbus_ref1.setDecimals(6)
        self.spinbox_vbus_ref1.setMaximum(50.0)
        self.spinbox_vbus_ref1.setSingleStep(0.0001)
        self.spinbox_vbus_ref1.setObjectName("spinbox_vbus_ref1")
        self.gridLayout_2.addWidget(self.spinbox_vbus_ref1, 4, 3, 1, 1)
        self.btn_vbus_ref1 = ToolButton(self.CardWidget_2)
        self.btn_vbus_ref1.setMinimumSize(QtCore.QSize(44, 44))
        self.btn_vbus_ref1.setIcon(icon)
        self.btn_vbus_ref1.setIconSize(QtCore.QSize(22, 22))
        self.btn_vbus_ref1.setObjectName("btn_vbus_ref1")
        self.gridLayout_2.addWidget(self.btn_vbus_ref1, 4, 4, 1, 1)
        self.label_speed_8 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_8.sizePolicy().hasHeightForWidth())
        self.label_speed_8.setSizePolicy(sizePolicy)
        self.label_speed_8.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_8.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_8.setObjectName("label_speed_8")
        self.gridLayout_2.addWidget(self.label_speed_8, 5, 0, 1, 1)
        self.label_speed_10 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_10.sizePolicy().hasHeightForWidth())
        self.label_speed_10.setSizePolicy(sizePolicy)
        self.label_speed_10.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_10.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_10.setObjectName("label_speed_10")
        self.gridLayout_2.addWidget(self.label_speed_10, 5, 1, 1, 2)
        self.sw_btn_ctrl_mode_select = SwitchButton(self.CardWidget_2)
        self.sw_btn_ctrl_mode_select.setObjectName("sw_btn_ctrl_mode_select")
        self.gridLayout_2.addWidget(self.sw_btn_ctrl_mode_select, 5, 3, 1, 1)
        self.label_speed_9 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_9.sizePolicy().hasHeightForWidth())
        self.label_speed_9.setSizePolicy(sizePolicy)
        self.label_speed_9.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_9.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_9.setObjectName("label_speed_9")
        self.gridLayout_2.addWidget(self.label_speed_9, 6, 0, 1, 1)
        self.label_speed_11 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_11.sizePolicy().hasHeightForWidth())
        self.label_speed_11.setSizePolicy(sizePolicy)
        self.label_speed_11.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_11.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_11.setObjectName("label_speed_11")
        self.gridLayout_2.addWidget(self.label_speed_11, 6, 1, 1, 2)
        self.sw_btn_ctrl_mode_select_2 = SwitchButton(self.CardWidget_2)
        self.sw_btn_ctrl_mode_select_2.setObjectName("sw_btn_ctrl_mode_select_2")
        self.gridLayout_2.addWidget(self.sw_btn_ctrl_mode_select_2, 6, 3, 1, 1)
        self.label_speed_12 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_12.sizePolicy().hasHeightForWidth())
        self.label_speed_12.setSizePolicy(sizePolicy)
        self.label_speed_12.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_12.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_12.setObjectName("label_speed_12")
        self.gridLayout_2.addWidget(self.label_speed_12, 7, 0, 1, 1)
        self.label_speed_13 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_13.sizePolicy().hasHeightForWidth())
        self.label_speed_13.setSizePolicy(sizePolicy)
        self.label_speed_13.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_13.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_13.setObjectName("label_speed_13")
        self.gridLayout_2.addWidget(self.label_speed_13, 7, 1, 1, 2)
        self.btn_start_resolver_calibration = PillPushButton(self.CardWidget_2)
        self.btn_start_resolver_calibration.setMinimumSize(QtCore.QSize(0, 44))
        self.btn_start_resolver_calibration.setCheckable(False)
        self.btn_start_resolver_calibration.setObjectName("btn_start_resolver_calibration")
        self.gridLayout_2.addWidget(self.btn_start_resolver_calibration, 7, 3, 1, 1)
        self.label_speed_14 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_14.sizePolicy().hasHeightForWidth())
        self.label_speed_14.setSizePolicy(sizePolicy)
        self.label_speed_14.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_14.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_14.setObjectName("label_speed_14")
        self.gridLayout_2.addWidget(self.label_speed_14, 8, 0, 1, 1)
        self.label_speed_15 = SubtitleLabel(self.CardWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_speed_15.sizePolicy().hasHeightForWidth())
        self.label_speed_15.setSizePolicy(sizePolicy)
        self.label_speed_15.setMinimumSize(QtCore.QSize(0, 0))
        self.label_speed_15.setText("")
        self.label_speed_15.setTextFormat(QtCore.Qt.MarkdownText)
        self.label_speed_15.setObjectName("label_speed_15")
        self.gridLayout_2.addWidget(self.label_speed_15, 8, 1, 1, 2)
        self.btn_save_param = PillPushButton(self.CardWidget_2)
        self.btn_save_param.setMinimumSize(QtCore.QSize(0, 44))
        self.btn_save_param.setCheckable(False)
        self.btn_save_param.setObjectName("btn_save_param")
        self.gridLayout_2.addWidget(self.btn_save_param, 8, 3, 1, 1)
        self.gridLayout_3.addWidget(self.CardWidget_2, 2, 2, 1, 2)
        self.SubtitleLabel_2 = SubtitleLabel(Form)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SubtitleLabel_2.sizePolicy().hasHeightForWidth())
        self.SubtitleLabel_2.setSizePolicy(sizePolicy)
        self.SubtitleLabel_2.setMinimumSize(QtCore.QSize(0, 50))
        self.SubtitleLabel_2.setMaximumSize(QtCore.QSize(16777215, 50))
        self.SubtitleLabel_2.setBaseSize(QtCore.QSize(0, 50))
        self.SubtitleLabel_2.setAlignment(QtCore.Qt.AlignCenter)
        self.SubtitleLabel_2.setObjectName("SubtitleLabel_2")
        self.gridLayout_3.addWidget(self.SubtitleLabel_2, 1, 2, 1, 2)
        self.SubtitleLabel = SubtitleLabel(Form)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SubtitleLabel.sizePolicy().hasHeightForWidth())
        self.SubtitleLabel.setSizePolicy(sizePolicy)
        self.SubtitleLabel.setMinimumSize(QtCore.QSize(0, 50))
        self.SubtitleLabel.setMaximumSize(QtCore.QSize(16777215, 50))
        self.SubtitleLabel.setAlignment(QtCore.Qt.AlignCenter)
        self.SubtitleLabel.setObjectName("SubtitleLabel")
        self.gridLayout_3.addWidget(self.SubtitleLabel, 1, 0, 1, 2)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.LargeTitleLabel.setText(_translate("Form", "电机控制器"))
        self.label_speed_2.setText(_translate("Form", "P"))
        self.label_speed_3.setText(_translate("Form", "I"))
        self.label_speed.setText(_translate("Form", "速度环："))
        self.label_speed_5.setText(_translate("Form", "电流环D："))
        self.label_speed_6.setText(_translate("Form", "电流环Q：     "))
        self.label_speed_7.setText(_translate("Form", "母线电压参考："))
        self.label_speed_8.setText(_translate("Form", "控制模式选择："))
        self.label_speed_10.setText(_translate("Form", "当前为速度环模式"))
        self.sw_btn_ctrl_mode_select.setText(_translate("Form", "速度环模式"))
        self.sw_btn_ctrl_mode_select.setOnText(_translate("Form", "电流环模式"))
        self.sw_btn_ctrl_mode_select.setOffText(_translate("Form", "速度环模式"))
        self.label_speed_9.setText(_translate("Form", "在线升级模式："))
        self.label_speed_11.setText(_translate("Form", "当前未启动升级模式"))
        self.sw_btn_ctrl_mode_select_2.setText(_translate("Form", "正常模式"))
        self.sw_btn_ctrl_mode_select_2.setOnText(_translate("Form", "升级模式"))
        self.sw_btn_ctrl_mode_select_2.setOffText(_translate("Form", "正常模式"))
        self.label_speed_12.setText(_translate("Form", "旋变零点校准："))
        self.label_speed_13.setText(_translate("Form", "当前未启动旋变校准"))
        self.btn_start_resolver_calibration.setText(_translate("Form", "启动旋变校准"))
        self.label_speed_14.setText(_translate("Form", "永久保存参数："))
        self.btn_save_param.setText(_translate("Form", "保存参数命令"))
        self.SubtitleLabel_2.setText(_translate("Form", "PID参数设置"))
        self.SubtitleLabel.setText(_translate("Form", "PID参数监控"))
from qfluentwidgets import CardWidget, DoubleSpinBox, LargeTitleLabel, PillPushButton, SubtitleLabel, SwitchButton, TableWidget, ToolButton
