使用中文和我交流。

项目简述：
我准备制作一个电机控制板上位机，这个上位机使用UDP和电机控制板通信，集成了电机控制板电机参数的示波器功能和参数预览及其配置功能。必要的Python库我已经安装了。
开发环境是VSCode+Python 3.11.4+Windows 11。
默认shell是powershell。
虚拟环境使用venv，虚拟环境目录为venv。
项目名称为UDP_Oscilloscope。
项目所在路径为`C:\Users\<USER>\Desktop\WorkPlace\MotorDrive_250kW\UDP_Oscilloscope`。
项目目标：
能够接收下位机上传的UDP数据，并且实现示波器功能，使得能够观察电机控制板的电机参数变化，同时能够对电机控制板的参数进行预览和配置。

UDP通信协议定义：
### 主数据包格式（最大大概位300字节（不固定））
| 字段 | 类型 | 字节序 | 描述 |
|------|------|--------|------|
| 包头 | 固定值 | 小端序 | `0x55AA` |
| 剩余长度 | `uint16_t` | 小端序 | 包含CRC的剩余部分长度 |
| 序号 | `uint16_t` | 小端序 | 递增序号，用于丢包/重复统计 |
| 数据包 | `uint8_t[]` | - | 子数据包内容 |
| CRC校验 | `uint16_t` | **小端序** | CRC-Modbus校验 |

测试写寄存器数据：
AA 55 0F 00 03 00 10 01 00 02 00 0A 00 00 00 0C 00 00 00 E2 5D

大数据包CRC校验失败则寻找下一个包头起始的位置开始CRC校验，直到找到下一个包头起始位置或者数据包结束，而不去把整个当前收到的数据缓存给丢弃，避免错误丢失数据。

大的数据包中包含多个小的数据包，只要大的数据包CRC校验通过小的数据包就假的不会有错误了，如下一个有三种小数据包分为配置上传、配置下传、数据包和电机采样值上传数据包。
每个小数据包的第一个字节为数据包的类型，数据包类型定义如下：
| 类型码 | 描述 |
|--------|------|
| `0xA1` | 电机采样值（uint16_t格式） |
| `0xA2` | 电机采样值（IEEE754 float32格式） |
| `0xF3` | 配置下传数据包 |
| `0xF4` | 配置上传数据包 |

### 子数据包格式详细定义

**类型0xA1: uint16_t电机采样数据（21字节）**
```
数据包类型: uint8_t (0xA1)
CH1-CH10: uint16_t × 10 (小端序)
```

**类型0xA2: float32电机采样数据（41字节）**
```
数据包类型: uint8_t (0xA2)  
CH1-CH10: float32_t × 10 (小端序)
```

**类型0x03: 配置下传数据包（25字节）**
| 字段 | 类型 | 描述 |
| --- | --- | --- |
|数据包类型 | uint8_t (0xF3) | 配置下传标识 |
|kp | float32_t 小端序 | PID参数kp |
|ki | float32_t 小端序 | PID参数ki |
|kd | float32_t 小端序 | PID参数kd |
|kp1 | float32_t 小端序 | PID参数kp1 |
|ki1 | float32_t 小端序 | PID参数ki1 |
|kd1 | float32_t 小端序 | PID参数kd1 |

**类型0x04: 配置上传数据包（25字节）**
| 字段 | 类型 | 描述 |
| --- | --- | --- |
|数据包类型 | uint8_t (0xF4) | 配置上传标识 |
|kp | float32_t 小端序 | PID参数kp |
|ki | float32_t 小端序 | PID参数ki |
|kd | float32_t 小端序 | PID参数kd |
|kp1 | float32_t 小端序 | PID参数kp1 |
|ki1 | float32_t 小端序 | PID参数ki1 |
|kd1 | float32_t 小端序 | PID参数kd1 |

具体要求：
1. 按照上述协议编写UDP接收数据的模块，完成数据的接收、解析、丢包率统计、重复率统计的功能，使用aioudp库来实现UDP通信,并且编写一个测试下位机模拟程序，测试上位机效果。
如上完成UDP数据的接收处理部分。
2. 然后是UI部分，如src\main_window\main_window.py所示的UI代码只完成了部分草稿，当然你需要使用pyqtgraph来实现波形显示功能，底层开启OpenGL加速提高刷新率scope_widget只是告诉你波形显示的控件位置。并且效果据说示波器的ROLL滚动模式，默认存储深度1GB（需要能够通过配置文件配置）。
然后我的思路是通过json配置文件来配置有多少给通道，例如我现在需要配置有CH1-CH10十个通道，则更新ch_setting_comboBox的内容为CH1-CH10，ch_setting_comboBox选中什么通道ch_ctrl_widget的tab_ch对应页面就显示什么通道的参数配置，包括tab_ch的tabName、垂直挡位spinbox、垂直偏移spinbox、单位label、当前通道颜色（这个我不知道用什么控件好你帮我实现，要能够显示当前配置的颜色还可以打开合适的小气泡设置颜色）等等。
其次ch_ctrl_widget的tab_ch对应页面有些功能的UI元素我还没有添加，包括通道的缩放比例，单位的选择，光标X1,X2,Y1,Y2的显示和设置，光标的显示和隐藏，光标的X1,X2,Y1,Y2的值显示等，光标的值和单位要更具当前选择的通道来正确显示。
3. 配置文件，上面这些配置都需要通过配置文件来配置，配置文件使用json格式，方便用户修改和扩展。加载配置文件失败则使用原先加载的或者默认配置文件。
4. 实现示波器的波形显示功能，使用pyqtgraph来实现，OpenGL加速，功能上面目前就只需要滚动模式显示即可（只需要渲染当前窗口区域的数据），
只是UI交互上面我希望默认就是滚动模式，并不需要实现专业示波器的那些复杂功能，我们现在主要就是需要滚动模式，当然能够通过disable_roll_radio_btn暂停自动滚动来方便观察当前想要观察的波形数据，而后台仍然在接收数据包并且缓存数据。当然要增加美观的Div的网格线和专业的示波器的风格一样方便我们用来读取数值大大小。
5. 我已经提供了其他项目的nuitka和pyinstaller打包工具的脚本build_nuitka.bat，build_pyinstaller.bat打包脚本据此修改即可。

技术要求：
1. UI界面使用PyQt5进行设计，我已经完成了界面的大体轮廓，见src\main_window\main_window.py所示的UI代码，其中scope_widget是波形显示的控件，当然你需要使用pyqtgraph来实现波形显示功能，底层开启OpenGL加速提高刷新率scope_widget只是告诉你波形显示的控件位置。
2. 整个PyQt5通过qasync来实现异步操作，通信通过aioudp来实现UDP通信，使用winloop来提高异步操作的效率。
3. 快速存储上面，我希望更具配置的存储深度，例如1GB，根据使用pyqtgraph的方案来合理的缓存历史数据，超过存储深度则丢弃最早的数据。预留后台永久固化存储的接口，可以通过配置文件选择是否开启后台持续永久固化存储的功能，避免阻塞示波器的绘图线程。
4. CRC使用crcmod库来实现CRC校验，使用CRC-Modbus算法。

代码生成要求：
代码要专业可靠，但是又要以最少的代码实现我们的功能从而降低维护成本。
注意不同功能的模块要分开，便于后期维护和扩展。
一次性完成代码，自行测试，没有问题后本地git提交。
注意后期维护和扩展的便利性，代码要有良好的注释和文档说明。

 模块化设计
 src/
├── main.py           # 主入口
├── communication/     # UDP通信模块
├── ui/               # 界面组件  
├── config/           # 配置管理
├── data/             # 数据处理与存储
└── utils/            # 工具类

功能验收

 UDP数据正确接收解析，统计准确
 示波器实时显示，滚动流畅
 参数配置界面完整可用
 配置文件正确加载保存
 下位机模拟器测试通过

性能指标

 界面刷新率 ≥ 120fps
 内存使用稳定，无泄漏
 CPU占用合理 (< 20%)
 数据处理延迟 < 10ms

代码质量
 模块划分清晰
 异常处理完整
 注释文档齐全
 Git提交记录清晰