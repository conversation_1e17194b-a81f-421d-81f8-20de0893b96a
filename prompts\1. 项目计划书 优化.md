使用中文和我交流。

## 项目概述
开发一个电机控制板上位机软件，具备UDP通信、实时示波器显示和参数配置功能。

**开发环境：**
- VSCode + Python 3.11.4 + Windows 11
- 虚拟环境：venv（目录：./venv）
- 默认Shell：PowerShell
- 项目路径：`C:\Users\<USER>\Desktop\WorkPlace\MotorDrive_250kW\UDP_Oscilloscope`

## UDP通信协议规范

### 主数据包格式（最大300字节）
| 字段 | 类型 | 字节序 | 描述 |
|------|------|--------|------|
| 包头 | 固定值 | 大端序 | `0x55AA` |
| 剩余长度 | `uint16_t` | 大端序 | 包含CRC的剩余部分长度 |
| 序号 | `uint16_t` | 大端序 | 递增序号，用于丢包/重复统计 |
| 数据包 | `uint8_t[]` | - | 子数据包内容 |
| CRC校验 | `uint16_t` | **小端序** | CRC-Modbus校验 |

### 子数据包类型定义
| 类型码 | 描述 |
|--------|------|
| `0x01` | 电机采样值（uint16_t格式） |
| `0x02` | 电机采样值（IEEE754 float32格式） |
| `0x03` | 配置下传数据包 |
| `0x04` | 配置上传数据包 |

### 子数据包格式详细定义

**类型0x01: uint16_t电机采样数据（21字节）**
```
数据包类型: uint8_t (0x01)
CH1-CH10: uint16_t × 10 (小端序)
```

**类型0x02: float32电机采样数据（41字节）**
```
数据包类型: uint8_t (0x02)  
CH1-CH10: float32_t × 10 (小端序)
```

**类型0x03: 配置下传数据包（25字节）**
| 字段 | 类型 | 描述 |
| --- | --- | --- |
|数据包类型 | uint8_t (0x03) | 配置下传标识 |
|kp | float32_t 小端序 | PID参数kp |
|ki | float32_t 小端序 | PID参数ki |
|kd | float32_t 小端序 | PID参数kd |
|kp1 | float32_t 小端序 | PID参数kp1 |
|ki1 | float32_t 小端序 | PID参数ki1 |
|kd1 | float32_t 小端序 | PID参数kd1 |

**类型0x04: 配置上传数据包（25字节）**
| 字段 | 类型 | 描述 |
| --- | --- | --- |
|数据包类型 | uint8_t (0x04) | 配置上传标识 |
|kp | float32_t 小端序 | PID参数kp |
|ki | float32_t 小端序 | PID参数ki |
|kd | float32_t 小端序 | PID参数kd |
|kp1 | float32_t 小端序 | PID参数kp1 |
|ki1 | float32_t 小端序 | PID参数ki1 |
|kd1 | float32_t 小端序 | PID参数kd1 |

## 开发任务详细要求

### 1. UDP通信模块开发
- 使用aioudp库实现异步UDP通信
- 实现数据包接收、解析、CRC校验
- 实现丢包率和重复包统计功能
- CRC校验失败时，寻找下一个包头位置继续解析，不丢弃整个缓存
- 开发下位机模拟器用于测试

### 2. UI界面完善
基于现有的`src\main_window\main_window.py`代码框架：

**示波器显示区域（scope_widget）：**
- 使用pyqtgraph实现波形显示，启用OpenGL加速
- 实现ROLL滚动模式，默认存储深度1GB（可配置）
- 添加专业示波器风格的网格线
- 支持通过disable_roll_radio_btn暂停/恢复滚动

**通道配置界面完善：**
- ch_setting_comboBox：根据配置文件动态生成通道列表（如CH1-CH10）
- ch_ctrl_widget的tab_ch页面需要添加以下控件：
  - 垂直挡位spinbox
  - 垂直偏移spinbox  
  - 单位选择combobox
  - 通道颜色显示和设置控件（颜色按钮+颜色选择器）
  - 缩放比例设置
  - 光标控制：X1,X2,Y1,Y2的显示、设置、显示/隐藏切换
  - 光标数值显示（根据当前选择通道显示正确的值和单位）

### 3. 配置文件系统
- 使用JSON格式配置文件
- 配置内容包括：
  - 通道数量和名称
  - 各通道的垂直挡位、偏移、单位、颜色
  - 存储深度设置
  - 永久存储开关
- 配置文件加载失败时使用默认配置
- 支持配置的动态加载和保存

### 4. 数据存储和处理
- 根据配置的存储深度（如1GB）管理历史数据缓存
- 超过存储深度时自动丢弃最早数据
- 预留后台永久存储接口，可通过配置开启
- 确保存储操作不阻塞UI绘图线程

### 5. 打包脚本适配
- 基于现有的build_nuitka.bat和build_pyinstaller.bat脚本
- 修改适配当前项目的打包需求

## 技术架构要求

**核心技术栈：**
- UI框架：PyQt5
- 异步处理：qasync + winloop
- UDP通信：aioudp
- 图形显示：pyqtgraph（OpenGL加速）

**模块化设计：**
```
src/
├── main.py              # 主入口文件
├── communication/       # UDP通信模块
│   ├── __init__.py
│   ├── udp_receiver.py  # UDP接收器
│   ├── protocol.py      # 协议解析
│   └── simulator.py     # 下位机模拟器
├── ui/                  # 界面组件
│   ├── __init__.py
│   └── main_window.py   # 主窗口
├── config/              # 配置管理
│   ├── __init__.py
│   ├── config_manager.py # 配置管理器
│   └── default_config.json # 默认配置
├── data/                # 数据处理与存储
│   ├── __init__.py
│   ├── data_buffer.py   # 数据缓存
│   └── storage.py       # 永久存储
└── utils/               # 工具类
    ├── __init__.py
    └── crc.py           # CRC校验工具
```

## 性能和质量要求

**性能指标：**
- 界面刷新率 ≥ 120fps
- 内存使用稳定，无内存泄漏
- CPU占用率 < 20%
- 数据处理延迟 < 10ms

**代码质量要求：**
- 模块划分清晰，职责单一
- 完整的异常处理机制
- 详细的代码注释和文档
- 专业可靠但代码简洁，降低维护成本

**验收标准：**
- UDP数据正确接收解析，统计功能准确
- 示波器实时显示流畅，滚动模式正常
- 参数配置界面功能完整可用
- 配置文件正确加载和保存
- 下位机模拟器测试通过

**交付要求：**
- 一次性完成所有功能代码
- 进行充分的自测试
- 完成后进行本地git提交
- 确保代码具备良好的可维护性和可扩展性