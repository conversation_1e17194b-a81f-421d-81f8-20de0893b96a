# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'src\ui\main_window.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(620, 584)
        self.gridLayout = QtWidgets.QGridLayout(Form)
        self.gridLayout.setObjectName("gridLayout")
        self.CardWidget = CardWidget(Form)
        self.CardWidget.setObjectName("CardWidget")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.CardWidget)
        self.verticalLayout.setObjectName("verticalLayout")
        self.gridLayout_2 = QtWidgets.QGridLayout()
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.CaptionLabel_2 = CaptionLabel(self.CardWidget)
        self.CaptionLabel_2.setObjectName("CaptionLabel_2")
        self.gridLayout_2.addWidget(self.CaptionLabel_2, 0, 2, 1, 1)
        self.hori_div_spinbox = DoubleSpinBox(self.CardWidget)
        self.hori_div_spinbox.setObjectName("hori_div_spinbox")
        self.gridLayout_2.addWidget(self.hori_div_spinbox, 0, 1, 1, 1)
        self.CaptionLabel = CaptionLabel(self.CardWidget)
        self.CaptionLabel.setObjectName("CaptionLabel")
        self.gridLayout_2.addWidget(self.CaptionLabel, 0, 0, 1, 1)
        self.verticalLayout.addLayout(self.gridLayout_2)
        self.sw_mode = SwitchButton(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.sw_mode.sizePolicy().hasHeightForWidth())
        self.sw_mode.setSizePolicy(sizePolicy)
        self.sw_mode.setObjectName("sw_mode")
        self.verticalLayout.addWidget(self.sw_mode)
        self.radiobtn_recording_wave_enable = RadioButton(self.CardWidget)
        self.radiobtn_recording_wave_enable.setAutoExclusive(False)
        self.radiobtn_recording_wave_enable.setObjectName("radiobtn_recording_wave_enable")
        self.verticalLayout.addWidget(self.radiobtn_recording_wave_enable)
        self.radioButton = RadioButton(self.CardWidget)
        self.radioButton.setAutoExclusive(False)
        self.radioButton.setObjectName("radioButton")
        self.verticalLayout.addWidget(self.radioButton)
        self.ch_setting_comboBox = ComboBox(self.CardWidget)
        self.ch_setting_comboBox.setObjectName("ch_setting_comboBox")
        self.verticalLayout.addWidget(self.ch_setting_comboBox)
        self.btn_recording_preview = PushButton(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_recording_preview.sizePolicy().hasHeightForWidth())
        self.btn_recording_preview.setSizePolicy(sizePolicy)
        self.btn_recording_preview.setObjectName("btn_recording_preview")
        self.verticalLayout.addWidget(self.btn_recording_preview)
        self.reload_conf_btn = HyperlinkButton(self.CardWidget)
        self.reload_conf_btn.setObjectName("reload_conf_btn")
        self.verticalLayout.addWidget(self.reload_conf_btn)
        self.global_ctrl_widget = QtWidgets.QWidget(self.CardWidget)
        self.global_ctrl_widget.setObjectName("global_ctrl_widget")
        self.gridLayout_3 = QtWidgets.QGridLayout(self.global_ctrl_widget)
        self.gridLayout_3.setObjectName("gridLayout_3")
        self.global_ctrl_widget_layout = QtWidgets.QVBoxLayout()
        self.global_ctrl_widget_layout.setObjectName("global_ctrl_widget_layout")
        self.gridLayout_3.addLayout(self.global_ctrl_widget_layout, 0, 0, 1, 1)
        self.verticalLayout.addWidget(self.global_ctrl_widget)
        self.ch_ctrl_widget = QtWidgets.QTabWidget(self.CardWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.ch_ctrl_widget.sizePolicy().hasHeightForWidth())
        self.ch_ctrl_widget.setSizePolicy(sizePolicy)
        self.ch_ctrl_widget.setMinimumSize(QtCore.QSize(0, 0))
        self.ch_ctrl_widget.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(12)
        self.ch_ctrl_widget.setFont(font)
        self.ch_ctrl_widget.setObjectName("ch_ctrl_widget")
        self.tab_ch = QtWidgets.QWidget()
        self.tab_ch.setObjectName("tab_ch")
        self.gridLayout_5 = QtWidgets.QGridLayout(self.tab_ch)
        self.gridLayout_5.setObjectName("gridLayout_5")
        self.ch_scroll_area_contents = ScrollArea(self.tab_ch)
        self.ch_scroll_area_contents.setFrameShape(QtWidgets.QFrame.NoFrame)
        self.ch_scroll_area_contents.setFrameShadow(QtWidgets.QFrame.Raised)
        self.ch_scroll_area_contents.setWidgetResizable(True)
        self.ch_scroll_area_contents.setObjectName("ch_scroll_area_contents")
        self.scrollAreaWidgetContents = QtWidgets.QWidget()
        self.scrollAreaWidgetContents.setGeometry(QtCore.QRect(0, 0, 212, 232))
        self.scrollAreaWidgetContents.setObjectName("scrollAreaWidgetContents")
        self.gridLayout_4 = QtWidgets.QGridLayout(self.scrollAreaWidgetContents)
        self.gridLayout_4.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_4.setObjectName("gridLayout_4")
        self.ch_scroll_area_contents_layout = QtWidgets.QVBoxLayout()
        self.ch_scroll_area_contents_layout.setObjectName("ch_scroll_area_contents_layout")
        self.gridLayout_4.addLayout(self.ch_scroll_area_contents_layout, 0, 0, 1, 1)
        self.ch_scroll_area_contents.setWidget(self.scrollAreaWidgetContents)
        self.gridLayout_5.addWidget(self.ch_scroll_area_contents, 0, 0, 1, 1)
        self.ch_ctrl_widget.addTab(self.tab_ch, "")
        self.verticalLayout.addWidget(self.ch_ctrl_widget)
        self.gridLayout.addWidget(self.CardWidget, 0, 1, 1, 1)
        self.scope_widget = QtWidgets.QWidget(Form)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.scope_widget.sizePolicy().hasHeightForWidth())
        self.scope_widget.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setFamily("Times New Roman")
        font.setPointSize(12)
        self.scope_widget.setFont(font)
        self.scope_widget.setObjectName("scope_widget")
        self.gridLayout.addWidget(self.scope_widget, 0, 0, 1, 1)

        self.retranslateUi(Form)
        self.ch_ctrl_widget.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.CaptionLabel_2.setText(_translate("Form", "1ms/div"))
        self.CaptionLabel.setText(_translate("Form", "时基挡位"))
        self.sw_mode.setText(_translate("Form", "实时波形显示模式"))
        self.sw_mode.setOnText(_translate("Form", "录波预览模式"))
        self.sw_mode.setOffText(_translate("Form", "实时波形显示模式"))
        self.radiobtn_recording_wave_enable.setText(_translate("Form", "录波使能"))
        self.radioButton.setText(_translate("Form", "自动滚动"))
        self.btn_recording_preview.setText(_translate("Form", "录波预览"))
        self.reload_conf_btn.setText(_translate("Form", "重新载入配置文件"))
        self.ch_ctrl_widget.setTabText(self.ch_ctrl_widget.indexOf(self.tab_ch), _translate("Form", "CH1"))
from qfluentwidgets import CaptionLabel, CardWidget, ComboBox, DoubleSpinBox, HyperlinkButton, PushButton, RadioButton, ScrollArea, SwitchButton
